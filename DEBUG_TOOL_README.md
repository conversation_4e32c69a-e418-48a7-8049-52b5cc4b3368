# SiliconFlow 语音克隆 API 调试工具

## 📖 概述

这是一个专门用于开发阶段调试 SiliconFlow 平台语音克隆接口的独立HTML页面。该工具提供了完整的用户界面来测试所有相关的API功能，包括音频上传、语音生成、音色管理等。

## 🚀 功能特性

### 1. 上传参考音频（语音克隆）
- **API接口**: `POST /v1/uploads/audio/voice`
- **功能**: 上传音频文件用于语音克隆
- **支持格式**: WAV、MP3、M4A、AAC
- **文件大小限制**: 最大 10MB
- **界面功能**:
  - 拖拽上传支持
  - 实时文件验证
  - 上传进度显示
  - 错误提示和状态反馈

### 2. 创建文本转语音请求
- **API接口**: `POST /v1/audio/speech`
- **功能**: 使用上传的参考音频生成语音
- **界面功能**:
  - 文本输入框
  - 音色选择下拉框
  - 音频播放器
  - 实时状态反馈

### 3. 获取参考音频列表
- **API接口**: `GET /v1/audio/voice/list`
- **功能**: 显示已上传的所有参考音频
- **界面功能**:
  - 网格布局展示音色列表
  - 显示音色名称、URI、模型、文本等信息
  - 一键刷新功能

### 4. 删除参考音频
- **API接口**: `POST /v1/audio/voice/deletions`
- **功能**: 删除不需要的参考音频
- **界面功能**:
  - 每个音色项都有删除按钮
  - 确认对话框防止误删
  - 删除后自动刷新列表

## 🛠️ 使用方法

### 1. 打开调试工具
直接在浏览器中打开 `debug-siliconflow-voice-clone.html` 文件即可使用。

### 2. 配置API密钥
在页面顶部的"API配置"区域输入您的 SiliconFlow API 密钥。

### 3. 上传参考音频
1. 输入音色名称和参考文本
2. 选择语音模型（推荐使用 CosyVoice 2.0）
3. 点击或拖拽上传音频文件
4. 点击"上传音频"按钮

### 4. 生成语音
1. 在右侧点击"刷新音色列表"获取可用音色
2. 在"文本转语音"区域输入要转换的文本
3. 选择要使用的音色
4. 点击"生成语音"按钮
5. 生成成功后可以直接播放音频

### 5. 管理音色
- 在音色列表中查看所有已上传的音色
- 点击红色"删除音色"按钮可以删除不需要的音色
- 删除前会有确认对话框

## 🔧 技术特性

### API集成
- 完整的错误处理和用户友好的错误提示
- 请求状态指示（加载中、成功、失败）
- 自动重试机制
- 详细的控制台日志记录

### 用户界面
- 响应式设计，支持桌面和移动设备
- 现代化的渐变色彩设计
- 直观的操作流程
- 实时状态反馈

### 文件处理
- 支持拖拽上传
- 文件格式和大小验证
- Base64编码转换
- 上传进度显示

## 📋 支持的语音模型

### CosyVoice 2.0 (推荐)
- **模型ID**: `FunAudioLLM/CosyVoice2-0.5B`
- **特性**:
  - 150ms 超低延迟
  - 支持中文方言（粤语、四川话等）
  - 支持英文、日语、韩语
  - 情感和方言细粒度控制
  - 发音错误率降低30%-50%

### MOSS-TTSD
- **模型ID**: `fnlp/MOSS-TTSD-v0.5`
- **特性**:
  - 双语支持（中文+英文）
  - 零样本双人声音克隆
  - 长时程语音生成（最长960秒）
  - 表现力丰富的对话语音
  - 适合AI播客制作

## 🔍 调试功能

### 控制台日志
工具会在浏览器控制台中输出详细的调试信息：
- API请求和响应数据
- 错误信息和堆栈跟踪
- 文件处理状态
- 性能指标

### 错误处理
- 网络错误自动重试
- API错误详细提示
- 文件验证错误提示
- 用户操作错误提示

## ⚠️ 注意事项

1. **开发专用**: 此工具仅用于开发阶段调试，完成开发后应该移除
2. **API密钥安全**: 请勿在生产环境中暴露API密钥
3. **文件大小限制**: 音频文件最大支持10MB
4. **浏览器兼容性**: 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
5. **网络要求**: 需要稳定的网络连接访问SiliconFlow API

## 📚 相关文档

- [SiliconFlow API 文档](https://docs.siliconflow.cn/cn/api-reference/audio/upload-voice)
- [语音合成用户指南](https://docs.siliconflow.cn/cn/userguide/capabilities/text-to-speech)

## 🐛 问题排查

### 常见问题

1. **上传失败**
   - 检查API密钥是否正确
   - 确认文件格式和大小符合要求
   - 检查网络连接

2. **音色列表为空**
   - 确认已成功上传音频文件
   - 点击"刷新音色列表"按钮
   - 检查API密钥权限

3. **语音生成失败**
   - 确认已选择有效的音色
   - 检查输入文本长度（建议不超过5000字符）
   - 确认网络连接稳定

### 调试步骤

1. 打开浏览器开发者工具（F12）
2. 查看控制台（Console）标签页的日志信息
3. 查看网络（Network）标签页的API请求详情
4. 根据错误信息进行相应的调整

## 📝 更新日志

### v1.0.0 (当前版本)
- 实现完整的SiliconFlow语音克隆API集成
- 支持音频上传、语音生成、音色管理功能
- 提供现代化的用户界面
- 包含完整的错误处理和状态反馈
