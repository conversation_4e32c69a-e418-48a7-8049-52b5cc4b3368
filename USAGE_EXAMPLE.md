# SiliconFlow 语音克隆调试工具使用示例

## 🎯 快速开始

### 1. 打开调试工具
直接在浏览器中打开 `debug-siliconflow-voice-clone.html` 文件。

### 2. 配置API密钥
在页面顶部输入您的 SiliconFlow API 密钥：
```
sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw
```

## 📝 完整测试流程

### 步骤1：上传参考音频
1. **输入音色名称**：例如 "我的专属音色"
2. **输入参考文本**：例如 "你好，这是我的声音样本，用于语音克隆测试。"
3. **选择语音模型**：推荐使用 "CosyVoice 2.0"
4. **上传音频文件**：
   - 支持格式：WAV、MP3、M4A、AAC
   - 文件大小：最大 10MB
   - 建议时长：10-30秒
   - 建议内容：清晰的人声，无背景噪音

### 步骤2：获取音色列表
1. 点击右侧的 **"刷新音色列表"** 按钮
2. 查看已上传的音色信息
3. 每个音色显示：
   - 音色名称
   - URI标识符
   - 使用的模型
   - 参考文本

### 步骤3：生成语音
1. **输入测试文本**：例如 "这是使用我的音色生成的语音测试。"
2. **选择音色**：从下拉框中选择刚上传的音色
3. **调整高级参数**（可选）：
   - **语音速度**：调节播放速度（0.25x - 4.0x）
   - **音频增益**：调节音量大小（-10dB 到 +10dB）
   - **输出格式**：选择音频格式（MP3/WAV/Opus/PCM）
   - **采样率**：根据格式选择合适的采样率
   - **使用预设**：快速应用常用配置
4. **查看参数预览**：确认当前配置正确
5. **点击生成语音**：等待处理完成
6. **播放音频**：使用内置播放器试听效果

### 步骤4：管理音色
1. **试听音色**：点击音色卡片中的 "🎵 试听" 按钮
2. **删除音色**：点击 "🗑️ 删除" 按钮（需要确认）

## 🔧 高级功能

### 快捷键操作
- **Ctrl/Cmd + Enter**：快速生成语音
- **Ctrl/Cmd + U**：选择音频文件
- **Ctrl/Cmd + R**：刷新音色列表

### API统计监控
右侧面板显示实时统计：
- 音频上传次数
- 语音生成次数
- 音色删除次数
- 错误发生次数

### 调试信息
打开浏览器开发者工具（F12）查看：
- 详细的API请求/响应日志
- 错误信息和堆栈跟踪
- 性能指标

## 📋 测试用例示例

### 测试用例1：基础语音克隆
```
音色名称：测试女声
参考文本：大家好，我是小助手，很高兴为您服务。
测试文本：今天天气真不错，适合出去走走。
预期结果：生成的语音应该保持原音色特征
```

### 测试用例2：多语言支持
```
音色名称：双语主播
参考文本：Hello everyone, 大家好，欢迎收听我们的节目。
测试文本：Welcome to our show, 今天我们来聊聊人工智能。
预期结果：中英文切换自然，音色保持一致
```

### 测试用例3：情感表达
```
音色名称：情感播音
参考文本：这真是太棒了！我感到非常兴奋和开心。
测试文本：恭喜你获得了这个奖项，这是你应得的荣誉！
预期结果：保持原有的情感色彩和语调
```

### 测试用例4：高级参数测试
```
音色名称：参数测试音色
参考文本：这是一个用于测试各种参数效果的音色样本。

子测试4.1 - 速度测试：
- 慢速：速度 0.7x，测试文本："慢慢地说话，每个字都很清楚。"
- 正常：速度 1.0x，测试文本："正常速度的语音合成效果。"
- 快速：速度 1.5x，测试文本："快速播报新闻内容。"

子测试4.2 - 音量测试：
- 低音量：增益 -5dB，测试文本："轻声细语的效果。"
- 正常音量：增益 0dB，测试文本："标准音量的语音。"
- 高音量：增益 +5dB，测试文本："响亮清晰的播报。"

子测试4.3 - 格式测试：
- 高质量：WAV格式，44.1kHz，测试文本："高保真音质测试。"
- 标准质量：MP3格式，44.1kHz，测试文本："标准压缩音质。"
- 高压缩：Opus格式，48kHz，测试文本："高压缩比音质。"
```

### 测试用例5：预设配置测试
```
使用不同预设配置测试同一文本：
测试文本：欢迎收听今天的新闻播报，我是主播小王。

1. 默认设置：速度1.0x，增益0dB，MP3格式
2. 慢速清晰：速度0.8x，增益+2dB，WAV格式
3. 快速紧凑：速度1.3x，增益-1dB，Opus格式
4. 高质量：速度1.0x，增益+1dB，WAV格式
5. 播客优化：速度1.1x，增益+0.5dB，MP3格式

预期结果：每种预设都应该产生符合其特点的音频效果
```

## ⚠️ 常见问题解决

### 问题1：上传失败
**症状**：点击上传后显示错误
**解决方案**：
1. 检查API密钥是否正确
2. 确认文件格式（WAV/MP3/M4A/AAC）
3. 检查文件大小（<10MB）
4. 确保网络连接正常

### 问题2：音色列表为空
**症状**：刷新后没有显示任何音色
**解决方案**：
1. 确认已成功上传音频
2. 检查API密钥权限
3. 查看控制台错误信息
4. 尝试重新上传音频

### 问题3：语音生成失败
**症状**：点击生成后报错
**解决方案**：
1. 确认已选择有效音色
2. 检查输入文本长度（建议<1000字符）
3. 尝试使用不同的音色
4. 检查网络连接稳定性

### 问题4：音频无法播放
**症状**：生成成功但无法播放
**解决方案**：
1. 检查浏览器音频权限
2. 尝试刷新页面
3. 使用不同浏览器测试
4. 检查系统音量设置

## 🔍 调试技巧

### 1. 网络请求监控
在开发者工具的 Network 标签页中：
- 查看API请求的详细信息
- 检查请求头和响应状态
- 分析响应时间和数据大小

### 2. 控制台日志分析
在 Console 标签页中查看：
- API调用的详细日志
- 错误信息和堆栈跟踪
- 性能指标和统计数据

### 3. 音频文件优化建议
- **采样率**：16kHz 或 22kHz
- **比特率**：128kbps 或更高
- **时长**：10-30秒最佳
- **内容**：清晰人声，避免背景音乐

## 📊 性能优化建议

### 1. 文件大小优化
- 使用适当的音频压缩
- 避免过长的音频文件
- 选择合适的音频格式

### 2. 网络优化
- 使用稳定的网络连接
- 避免在网络高峰期测试
- 考虑使用代理加速

### 3. 批量测试
- 准备多个测试音频文件
- 使用不同的文本内容测试
- 记录测试结果进行对比

## 📈 测试报告模板

```
测试日期：2024-01-XX
API密钥：sk-xxx...xxx
测试环境：Chrome 120.0 / macOS 14.0

测试结果：
┌─────────────────┬──────────┬──────────┬──────────┐
│ 功能            │ 成功次数 │ 失败次数 │ 成功率   │
├─────────────────┼──────────┼──────────┼──────────┤
│ 音频上传        │ 5        │ 0        │ 100%     │
│ 语音生成        │ 8        │ 1        │ 89%      │
│ 音色删除        │ 2        │ 0        │ 100%     │
│ 音色列表获取    │ 10       │ 0        │ 100%     │
└─────────────────┴──────────┴──────────┴──────────┘

问题记录：
1. 语音生成失败1次，原因：网络超时
2. 建议：增加重试机制

总体评价：API稳定性良好，功能完整
```

## 🚀 下一步计划

完成调试后，可以考虑：
1. 将API集成到主应用中
2. 添加用户界面优化
3. 实现批量处理功能
4. 添加音频质量评估
5. 集成到生产环境
