<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SiliconFlow 语音克隆 API 调试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .api-config {
            background: #f8fafc;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 30px;
            border: 2px solid #e2e8f0;
        }

        .api-config h3 {
            color: #2d3748;
            margin-bottom: 16px;
            font-size: 1.3rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .btn-danger:hover {
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .section h3 {
            color: #1f2937;
            margin-bottom: 20px;
            font-size: 1.4rem;
            border-bottom: 2px solid #f3f4f6;
            padding-bottom: 10px;
        }

        .status {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-weight: 500;
        }

        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .status.loading {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .voice-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .voice-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.2s;
        }

        .voice-item:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .voice-item h4 {
            color: #1f2937;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .voice-item p {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 4px;
        }

        .audio-player {
            margin-top: 16px;
            width: 100%;
        }

        .file-upload {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            transition: all 0.2s;
            cursor: pointer;
        }

        .file-upload:hover {
            border-color: #667eea;
            background: #f8fafc;
        }

        .file-upload.dragover {
            border-color: #667eea;
            background: #eff6ff;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 12px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
        }

        .hidden {
            display: none;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎙️ SiliconFlow 语音克隆调试工具</h1>
            <p>专业的语音克隆API接口测试平台 - 开发调试专用</p>
        </div>

        <div class="content">
            <!-- API 配置区域 -->
            <div class="api-config">
                <h3>🔧 API 配置</h3>
                <div class="form-group">
                    <label for="apiKey">API 密钥:</label>
                    <input type="password" id="apiKey" placeholder="请输入您的 SiliconFlow API 密钥" 
                           value="sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw">
                </div>
                <div class="form-group">
                    <label for="baseUrl">API 基础URL:</label>
                    <input type="text" id="baseUrl" value="https://api.siliconflow.cn/v1" readonly>
                </div>
            </div>

            <div class="grid">
                <!-- 左侧：上传和语音生成 -->
                <div>
                    <!-- 上传参考音频 -->
                    <div class="section">
                        <h3>📤 上传参考音频</h3>
                        <div class="form-group">
                            <label for="voiceName">音色名称:</label>
                            <input type="text" id="voiceName" placeholder="请输入音色名称">
                        </div>
                        <div class="form-group">
                            <label for="voiceText">参考文本:</label>
                            <textarea id="voiceText" placeholder="请输入音频对应的文字内容"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="voiceModel">语音模型:</label>
                            <select id="voiceModel">
                                <option value="FunAudioLLM/CosyVoice2-0.5B">CosyVoice 2.0 (推荐)</option>
                                <option value="fnlp/MOSS-TTSD-v0.5">MOSS-TTSD</option>
                            </select>
                        </div>
                        <div class="file-upload" id="fileUpload">
                            <p>📁 点击选择音频文件或拖拽到此处</p>
                            <p style="font-size: 0.9rem; color: #6b7280; margin-top: 8px;">
                                支持 WAV、MP3、M4A、AAC 格式，最大 10MB
                            </p>
                            <input type="file" id="audioFile" accept="audio/*" style="display: none;">
                        </div>
                        <div id="uploadProgress" class="hidden">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                        </div>
                        <button class="btn" id="uploadBtn" disabled>
                            <span class="loading-spinner hidden" id="uploadSpinner"></span>
                            上传音频
                        </button>
                        <div id="uploadStatus"></div>
                    </div>

                    <!-- 文本转语音 -->
                    <div class="section">
                        <h3>🎵 文本转语音</h3>
                        <div class="form-group">
                            <label for="ttsText">要转换的文本:</label>
                            <textarea id="ttsText" placeholder="请输入要转换为语音的文本">你好，这是一个语音合成测试。</textarea>
                        </div>
                        <div class="form-group">
                            <label for="selectedVoice">选择音色:</label>
                            <select id="selectedVoice">
                                <option value="">请先获取音色列表</option>
                            </select>
                        </div>

                        <!-- 高级参数控制 -->
                        <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; margin: 16px 0; background: #f9fafb;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <h4 style="margin: 0; color: #374151; font-size: 1rem;">🎛️ 高级参数</h4>
                                <div>
                                    <select id="paramPresets" style="padding: 4px 8px; border: 1px solid #d1d5db; border-radius: 4px; font-size: 0.9rem;">
                                        <option value="">选择预设</option>
                                        <option value="default">默认设置</option>
                                        <option value="slow_clear">慢速清晰</option>
                                        <option value="fast_compact">快速紧凑</option>
                                        <option value="high_quality">高质量</option>
                                        <option value="podcast">播客优化</option>
                                    </select>
                                    <button class="btn btn-secondary" onclick="resetToDefaults()" style="margin-left: 8px; padding: 4px 12px; font-size: 0.9rem;">重置</button>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                                <div class="form-group" style="margin-bottom: 12px;">
                                    <label for="speechSpeed">语音速度 (0.25-4.0):</label>
                                    <input type="range" id="speechSpeed" min="0.25" max="4.0" step="0.05" value="1.0"
                                           style="width: 100%; margin-bottom: 4px;">
                                    <div style="display: flex; justify-content: space-between; font-size: 0.8rem; color: #6b7280;">
                                        <span>0.25x</span>
                                        <span id="speedValue">1.0x</span>
                                        <span>4.0x</span>
                                    </div>
                                </div>

                                <div class="form-group" style="margin-bottom: 12px;">
                                    <label for="speechGain">音频增益 (-10 到 10 dB):</label>
                                    <input type="range" id="speechGain" min="-10" max="10" step="0.5" value="0"
                                           style="width: 100%; margin-bottom: 4px;">
                                    <div style="display: flex; justify-content: space-between; font-size: 0.8rem; color: #6b7280;">
                                        <span>-10dB</span>
                                        <span id="gainValue">0dB</span>
                                        <span>+10dB</span>
                                    </div>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                                <div class="form-group" style="margin-bottom: 0;">
                                    <label for="responseFormat">输出格式:</label>
                                    <select id="responseFormat">
                                        <option value="mp3">MP3 (推荐)</option>
                                        <option value="wav">WAV (无损)</option>
                                        <option value="opus">Opus (高压缩)</option>
                                        <option value="pcm">PCM (原始)</option>
                                    </select>
                                </div>

                                <div class="form-group" style="margin-bottom: 0;">
                                    <label for="sampleRate">采样率:</label>
                                    <select id="sampleRate">
                                        <option value="44100">44.1kHz (默认)</option>
                                        <option value="32000">32kHz</option>
                                        <option value="24000">24kHz</option>
                                        <option value="16000">16kHz</option>
                                        <option value="8000">8kHz</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 参数预览 -->
                        <div id="paramPreview" style="background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 6px; padding: 12px; margin: 12px 0; font-size: 0.9rem; color: #1e40af;">
                            <strong>当前配置:</strong> <span id="paramPreviewText">速度: 1.00x | 增益: 0dB | 格式: MP3 | 采样率: 44.1kHz</span>
                        </div>

                        <button class="btn" id="generateBtn">
                            <span class="loading-spinner hidden" id="generateSpinner"></span>
                            生成语音
                        </button>
                        <div id="generateStatus"></div>
                        <audio id="audioPlayer" controls class="audio-player hidden"></audio>
                    </div>
                </div>

                <!-- 右侧：音色管理 -->
                <div>
                    <!-- API 状态面板 -->
                    <div class="section">
                        <h3>📊 API 使用统计</h3>
                        <div id="apiStatsPanel" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 16px;">
                            <div style="background: #f3f4f6; padding: 12px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #059669;" id="uploadCount">0</div>
                                <div style="font-size: 0.9rem; color: #6b7280;">音频上传</div>
                            </div>
                            <div style="background: #f3f4f6; padding: 12px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #0891b2;" id="generationCount">0</div>
                                <div style="font-size: 0.9rem; color: #6b7280;">语音生成</div>
                            </div>
                            <div style="background: #f3f4f6; padding: 12px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #dc2626;" id="deletionCount">0</div>
                                <div style="font-size: 0.9rem; color: #6b7280;">音色删除</div>
                            </div>
                            <div style="background: #f3f4f6; padding: 12px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #ea580c;" id="errorCount">0</div>
                                <div style="font-size: 0.9rem; color: #6b7280;">错误次数</div>
                            </div>
                        </div>
                        <button class="btn btn-secondary" onclick="resetApiStats()">重置统计</button>
                    </div>

                    <!-- 音色列表 -->
                    <div class="section">
                        <h3>🎭 音色列表管理</h3>
                        <button class="btn" id="refreshVoicesBtn">
                            <span class="loading-spinner hidden" id="refreshSpinner"></span>
                            刷新音色列表
                        </button>
                        <div id="voiceListStatus"></div>
                        <div id="voiceList" class="voice-list"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentVoices = [];
        
        // DOM 元素
        const elements = {
            apiKey: document.getElementById('apiKey'),
            baseUrl: document.getElementById('baseUrl'),
            voiceName: document.getElementById('voiceName'),
            voiceText: document.getElementById('voiceText'),
            voiceModel: document.getElementById('voiceModel'),
            fileUpload: document.getElementById('fileUpload'),
            audioFile: document.getElementById('audioFile'),
            uploadBtn: document.getElementById('uploadBtn'),
            uploadProgress: document.getElementById('uploadProgress'),
            progressFill: document.getElementById('progressFill'),
            uploadStatus: document.getElementById('uploadStatus'),
            uploadSpinner: document.getElementById('uploadSpinner'),
            ttsText: document.getElementById('ttsText'),
            selectedVoice: document.getElementById('selectedVoice'),
            generateBtn: document.getElementById('generateBtn'),
            generateStatus: document.getElementById('generateStatus'),
            generateSpinner: document.getElementById('generateSpinner'),
            audioPlayer: document.getElementById('audioPlayer'),
            refreshVoicesBtn: document.getElementById('refreshVoicesBtn'),
            refreshSpinner: document.getElementById('refreshSpinner'),
            voiceListStatus: document.getElementById('voiceListStatus'),
            voiceList: document.getElementById('voiceList'),
            // 新增的高级参数元素
            speechSpeed: document.getElementById('speechSpeed'),
            speedValue: document.getElementById('speedValue'),
            speechGain: document.getElementById('speechGain'),
            gainValue: document.getElementById('gainValue'),
            responseFormat: document.getElementById('responseFormat'),
            sampleRate: document.getElementById('sampleRate')
        };

        // 工具函数
        function showStatus(element, message, type = 'loading') {
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearStatus(element) {
            element.innerHTML = '';
        }

        function showSpinner(spinner, show = true) {
            if (show) {
                spinner.classList.remove('hidden');
            } else {
                spinner.classList.add('hidden');
            }
        }

        function getApiKey() {
            return elements.apiKey.value.trim();
        }

        function getBaseUrl() {
            return elements.baseUrl.value.trim();
        }

        // 文件上传处理
        elements.fileUpload.addEventListener('click', () => {
            elements.audioFile.click();
        });

        elements.fileUpload.addEventListener('dragover', (e) => {
            e.preventDefault();
            elements.fileUpload.classList.add('dragover');
        });

        elements.fileUpload.addEventListener('dragleave', () => {
            elements.fileUpload.classList.remove('dragover');
        });

        elements.fileUpload.addEventListener('drop', (e) => {
            e.preventDefault();
            elements.fileUpload.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                elements.audioFile.files = files;
                handleFileSelect();
            }
        });

        elements.audioFile.addEventListener('change', handleFileSelect);

        // 高级参数控制事件监听
        elements.speechSpeed.addEventListener('input', () => {
            elements.speedValue.textContent = `${parseFloat(elements.speechSpeed.value).toFixed(2)}x`;
            updateParamPreview();
        });

        elements.speechGain.addEventListener('input', () => {
            const gain = parseFloat(elements.speechGain.value);
            elements.gainValue.textContent = `${gain >= 0 ? '+' : ''}${gain}dB`;
            updateParamPreview();
        });

        // 响应格式和采样率变化时也更新预览
        elements.responseFormat.addEventListener('change', updateParamPreview);
        elements.sampleRate.addEventListener('change', updateParamPreview);

        // 响应格式变化时自动调整采样率选项
        elements.responseFormat.addEventListener('change', updateSampleRateOptions);

        function updateSampleRateOptions() {
            const format = elements.responseFormat.value;
            const sampleRateSelect = elements.sampleRate;

            // 清空现有选项
            sampleRateSelect.innerHTML = '';

            switch (format) {
                case 'opus':
                    sampleRateSelect.innerHTML = '<option value="48000">48kHz (仅支持)</option>';
                    break;
                case 'mp3':
                    sampleRateSelect.innerHTML = `
                        <option value="44100">44.1kHz (默认)</option>
                        <option value="32000">32kHz</option>
                    `;
                    break;
                case 'wav':
                case 'pcm':
                    sampleRateSelect.innerHTML = `
                        <option value="44100">44.1kHz (默认)</option>
                        <option value="32000">32kHz</option>
                        <option value="24000">24kHz</option>
                        <option value="16000">16kHz</option>
                        <option value="8000">8kHz</option>
                    `;
                    break;
                default:
                    sampleRateSelect.innerHTML = '<option value="44100">44.1kHz (默认)</option>';
            }
        }

        // 初始化采样率选项
        updateSampleRateOptions();

        // 参数预设功能
        const paramPresets = {
            default: {
                speed: 1.0,
                gain: 0.0,
                response_format: 'mp3',
                sample_rate: 44100
            },
            slow_clear: {
                speed: 0.8,
                gain: 2.0,
                response_format: 'wav',
                sample_rate: 44100
            },
            fast_compact: {
                speed: 1.3,
                gain: -1.0,
                response_format: 'opus',
                sample_rate: 48000
            },
            high_quality: {
                speed: 1.0,
                gain: 1.0,
                response_format: 'wav',
                sample_rate: 44100
            },
            podcast: {
                speed: 1.1,
                gain: 0.5,
                response_format: 'mp3',
                sample_rate: 44100
            }
        };

        // 预设选择事件
        document.getElementById('paramPresets').addEventListener('change', (e) => {
            const presetName = e.target.value;
            if (presetName && paramPresets[presetName]) {
                applyPreset(paramPresets[presetName]);
            }
        });

        function applyPreset(preset) {
            elements.speechSpeed.value = preset.speed;
            elements.speechGain.value = preset.gain;
            elements.responseFormat.value = preset.response_format;

            // 更新显示值
            elements.speedValue.textContent = `${preset.speed.toFixed(2)}x`;
            const gain = preset.gain;
            elements.gainValue.textContent = `${gain >= 0 ? '+' : ''}${gain}dB`;

            // 更新采样率选项并设置值
            updateSampleRateOptions();
            elements.sampleRate.value = preset.sample_rate;

            console.log('已应用预设:', preset);
        }

        function resetToDefaults() {
            applyPreset(paramPresets.default);
            document.getElementById('paramPresets').value = '';
            console.log('已重置为默认参数');
        }

        // 验证高级参数
        function validateAdvancedParams() {
            const speed = parseFloat(elements.speechSpeed.value);
            const gain = parseFloat(elements.speechGain.value);
            const format = elements.responseFormat.value;
            const sampleRate = parseInt(elements.sampleRate.value);

            // 验证速度范围
            if (speed < 0.25 || speed > 4.0) {
                return {
                    valid: false,
                    error: '语音速度必须在 0.25 到 4.0 之间'
                };
            }

            // 验证增益范围
            if (gain < -10 || gain > 10) {
                return {
                    valid: false,
                    error: '音频增益必须在 -10dB 到 +10dB 之间'
                };
            }

            // 验证格式和采样率的组合
            const validCombinations = {
                'opus': [48000],
                'mp3': [32000, 44100],
                'wav': [8000, 16000, 24000, 32000, 44100],
                'pcm': [8000, 16000, 24000, 32000, 44100]
            };

            if (!validCombinations[format] || !validCombinations[format].includes(sampleRate)) {
                return {
                    valid: false,
                    error: `${format.toUpperCase()} 格式不支持 ${sampleRate}Hz 采样率`
                };
            }

            return { valid: true };
        }

        // 获取当前参数配置的描述
        function getCurrentParamsDescription() {
            const speed = parseFloat(elements.speechSpeed.value);
            const gain = parseFloat(elements.speechGain.value);
            const format = elements.responseFormat.value.toUpperCase();
            const sampleRate = parseInt(elements.sampleRate.value);

            return `速度: ${speed.toFixed(2)}x | 增益: ${gain >= 0 ? '+' : ''}${gain}dB | 格式: ${format} | 采样率: ${(sampleRate/1000).toFixed(1)}kHz`;
        }

        // 更新参数预览
        function updateParamPreview() {
            const previewElement = document.getElementById('paramPreviewText');
            if (previewElement) {
                previewElement.textContent = getCurrentParamsDescription();

                // 验证参数并更新预览样式
                const validation = validateAdvancedParams();
                const previewContainer = document.getElementById('paramPreview');

                if (validation.valid) {
                    previewContainer.style.background = '#eff6ff';
                    previewContainer.style.borderColor = '#bfdbfe';
                    previewContainer.style.color = '#1e40af';
                } else {
                    previewContainer.style.background = '#fef2f2';
                    previewContainer.style.borderColor = '#fecaca';
                    previewContainer.style.color = '#dc2626';
                    previewElement.textContent = `❌ ${validation.error}`;
                }
            }
        }

        function handleFileSelect() {
            const file = elements.audioFile.files[0];
            if (file) {
                // 验证文件
                const validation = validateAudioFile(file);
                if (!validation.valid) {
                    showStatus(elements.uploadStatus, validation.error, 'error');
                    elements.uploadBtn.disabled = true;
                    return;
                }

                // 创建音频预览
                const audioUrl = URL.createObjectURL(file);
                elements.fileUpload.innerHTML = `
                    <p>✅ 已选择文件: ${file.name}</p>
                    <p style="font-size: 0.9rem; color: #6b7280; margin-top: 8px;">
                        大小: ${(file.size / 1024 / 1024).toFixed(2)} MB | 类型: ${file.type}
                    </p>
                    <audio controls style="width: 100%; margin-top: 12px;">
                        <source src="${audioUrl}" type="${file.type}">
                        您的浏览器不支持音频播放。
                    </audio>
                `;
                elements.uploadBtn.disabled = false;
                clearStatus(elements.uploadStatus);
            }
        }

        function validateAudioFile(file) {
            const allowedTypes = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/m4a', 'audio/aac'];
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (!allowedTypes.includes(file.type)) {
                return {
                    valid: false,
                    error: '不支持的音频格式，请使用 WAV、MP3、M4A 或 AAC 格式'
                };
            }

            if (file.size > maxSize) {
                return {
                    valid: false,
                    error: '文件大小超过限制，请使用小于 10MB 的音频文件'
                };
            }

            return { valid: true };
        }

        // 将文件转换为base64
        function convertFileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    resolve(reader.result);
                };
                reader.onerror = () => {
                    reject(new Error('文件读取失败'));
                };
                reader.readAsDataURL(file);
            });
        }

        // 生成随机customName
        function generateCustomName() {
            return Math.random().toString(36).substring(2, 10);
        }

        // 上传音频文件
        async function uploadAudio() {
            const apiKey = getApiKey();
            if (!apiKey) {
                showStatus(elements.uploadStatus, '请先配置 API 密钥', 'error');
                return;
            }

            const file = elements.audioFile.files[0];
            if (!file) {
                showStatus(elements.uploadStatus, '请先选择音频文件', 'error');
                return;
            }

            const voiceName = elements.voiceName.value.trim();
            if (!voiceName) {
                showStatus(elements.uploadStatus, '请输入音色名称', 'error');
                return;
            }

            const voiceText = elements.voiceText.value.trim();
            if (!voiceText) {
                showStatus(elements.uploadStatus, '请输入参考文本', 'error');
                return;
            }

            try {
                elements.uploadBtn.disabled = true;
                showSpinner(elements.uploadSpinner, true);
                showStatus(elements.uploadStatus, '正在上传音频文件...', 'loading');

                // 转换文件为base64
                const base64Audio = await convertFileToBase64(file);
                const customName = generateCustomName();

                const requestBody = {
                    model: elements.voiceModel.value,
                    customName: customName,
                    audio: base64Audio,
                    text: voiceText
                };

                console.log('上传请求:', {
                    model: requestBody.model,
                    customName: customName,
                    textLength: voiceText.length,
                    audioSize: file.size,
                    audioType: file.type
                });

                const response = await fetch(`${getBaseUrl()}/uploads/audio/voice`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API 错误响应:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log('上传响应:', data);

                showStatus(elements.uploadStatus, `✅ 音频上传成功！音色URI: ${data.uri}`, 'success');
                updateApiStats('uploads', true);

                // 刷新音色列表
                await refreshVoiceList();

            } catch (error) {
                console.error('上传错误:', error);
                const detailedError = getDetailedErrorMessage(error, error.response);
                showStatus(elements.uploadStatus, `❌ 上传失败: ${detailedError}`, 'error');
                updateApiStats('uploads', false);
            } finally {
                elements.uploadBtn.disabled = false;
                showSpinner(elements.uploadSpinner, false);
            }
        }

        // 获取音色列表
        async function refreshVoiceList() {
            const apiKey = getApiKey();
            if (!apiKey) {
                showStatus(elements.voiceListStatus, '请先配置 API 密钥', 'error');
                return;
            }

            try {
                elements.refreshVoicesBtn.disabled = true;
                showSpinner(elements.refreshSpinner, true);
                showStatus(elements.voiceListStatus, '正在获取音色列表...', 'loading');

                const response = await fetch(`${getBaseUrl()}/audio/voice/list`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API 错误响应:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log('音色列表响应:', data);

                if (!data.result || !Array.isArray(data.result)) {
                    throw new Error('响应格式无效');
                }

                currentVoices = data.result;
                displayVoiceList(currentVoices);
                updateVoiceSelect(currentVoices);

                showStatus(elements.voiceListStatus, `✅ 成功获取 ${currentVoices.length} 个音色`, 'success');

            } catch (error) {
                console.error('获取音色列表错误:', error);
                showStatus(elements.voiceListStatus, `❌ 获取失败: ${error.message}`, 'error');
            } finally {
                elements.refreshVoicesBtn.disabled = false;
                showSpinner(elements.refreshSpinner, false);
            }
        }

        // 显示音色列表
        function displayVoiceList(voices) {
            if (voices.length === 0) {
                elements.voiceList.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 20px;">暂无音色数据</p>';
                return;
            }

            elements.voiceList.innerHTML = voices.map((voice, index) => `
                <div class="voice-item">
                    <h4>${voice.customName || `音色 ${index + 1}`}</h4>
                    <p><strong>URI:</strong> ${voice.uri}</p>
                    <p><strong>模型:</strong> ${voice.model}</p>
                    <p><strong>文本:</strong> ${voice.text}</p>
                    <div style="margin-top: 12px;">
                        <button class="btn btn-secondary" onclick="testVoicePreview('${voice.uri}', '${voice.text}')" style="margin-right: 8px;">
                            🎵 试听
                        </button>
                        <button class="btn btn-danger" onclick="deleteVoice('${voice.uri}')">
                            🗑️ 删除
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 更新音色选择下拉框
        function updateVoiceSelect(voices) {
            elements.selectedVoice.innerHTML = '<option value="">请选择音色</option>' +
                voices.map(voice => `
                    <option value="${voice.uri}">${voice.customName || voice.uri}</option>
                `).join('');
        }

        // 删除音色
        async function deleteVoice(uri) {
            const apiKey = getApiKey();
            if (!apiKey) {
                alert('请先配置 API 密钥');
                return;
            }

            if (!confirm(`确定要删除音色 ${uri} 吗？此操作无法撤销！`)) {
                return;
            }

            try {
                const response = await fetch(`${getBaseUrl()}/audio/voice/deletions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ uri: uri })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('删除API错误响应:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                alert('✅ 音色删除成功！');
                updateApiStats('deletions', true);
                await refreshVoiceList();

            } catch (error) {
                console.error('删除音色错误:', error);
                const detailedError = getDetailedErrorMessage(error, error.response);
                alert(`❌ 删除失败: ${detailedError}`);
                updateApiStats('deletions', false);
            }
        }

        // 生成语音
        async function generateSpeech() {
            const apiKey = getApiKey();
            if (!apiKey) {
                showStatus(elements.generateStatus, '请先配置 API 密钥', 'error');
                return;
            }

            const text = elements.ttsText.value.trim();
            if (!text) {
                showStatus(elements.generateStatus, '请输入要转换的文本', 'error');
                return;
            }

            const voiceUri = elements.selectedVoice.value;
            if (!voiceUri) {
                showStatus(elements.generateStatus, '请选择音色', 'error');
                return;
            }

            // 验证高级参数
            const paramValidation = validateAdvancedParams();
            if (!paramValidation.valid) {
                showStatus(elements.generateStatus, `参数错误: ${paramValidation.error}`, 'error');
                return;
            }

            try {
                elements.generateBtn.disabled = true;
                showSpinner(elements.generateSpinner, true);
                showStatus(elements.generateStatus, '正在生成语音...', 'loading');

                // 构建请求体，包含所有参数
                const requestBody = {
                    model: elements.voiceModel.value,
                    input: text,
                    voice: voiceUri,
                    speed: parseFloat(elements.speechSpeed.value),
                    gain: parseFloat(elements.speechGain.value),
                    response_format: elements.responseFormat.value,
                    sample_rate: parseInt(elements.sampleRate.value)
                };

                console.log('语音生成请求:', requestBody);
                console.log('当前参数配置:', getCurrentParamsDescription());

                const response = await fetch(`${getBaseUrl()}/audio/speech`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API 错误响应:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                // 检查响应类型
                const contentType = response.headers.get('content-type');

                if (contentType?.includes('audio/')) {
                    // 直接返回音频数据
                    const audioData = await response.arrayBuffer();
                    const audioBlob = new Blob([audioData], { type: 'audio/mpeg' });
                    const audioUrl = URL.createObjectURL(audioBlob);

                    elements.audioPlayer.src = audioUrl;
                    elements.audioPlayer.classList.remove('hidden');

                    showStatus(elements.generateStatus, '✅ 语音生成成功！', 'success');
                    updateApiStats('generations', true);
                } else {
                    // JSON 响应
                    const data = await response.json();
                    console.log('语音生成响应:', data);

                    if (data.error) {
                        throw new Error(data.error.message || '语音生成失败');
                    }

                    if (data.url || data.audio_url) {
                        elements.audioPlayer.src = data.url || data.audio_url;
                        elements.audioPlayer.classList.remove('hidden');
                        showStatus(elements.generateStatus, '✅ 语音生成成功！', 'success');
                        updateApiStats('generations', true);
                    } else {
                        throw new Error('响应中没有音频数据');
                    }
                }

            } catch (error) {
                console.error('语音生成错误:', error);
                const detailedError = getDetailedErrorMessage(error, error.response);
                showStatus(elements.generateStatus, `❌ 生成失败: ${detailedError}`, 'error');
                updateApiStats('generations', false);
            } finally {
                elements.generateBtn.disabled = false;
                showSpinner(elements.generateSpinner, false);
            }
        }

        // 事件监听器
        elements.uploadBtn.addEventListener('click', uploadAudio);
        elements.refreshVoicesBtn.addEventListener('click', refreshVoiceList);
        elements.generateBtn.addEventListener('click', generateSpeech);

        // 测试API连接
        async function testApiConnection() {
            const apiKey = getApiKey();
            if (!apiKey) {
                showStatus(elements.voiceListStatus, '请先配置 API 密钥', 'error');
                return false;
            }

            try {
                const response = await fetch(`${getBaseUrl()}/audio/voice/list`, {
                    method: 'HEAD',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                    }
                });

                return response.ok;
            } catch (error) {
                console.error('API连接测试失败:', error);
                return false;
            }
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter 快速生成语音
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                if (!elements.generateBtn.disabled) {
                    generateSpeech();
                }
            }

            // Ctrl/Cmd + U 快速上传
            if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                e.preventDefault();
                if (!elements.uploadBtn.disabled) {
                    elements.audioFile.click();
                }
            }

            // Ctrl/Cmd + R 刷新音色列表
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                if (!elements.refreshVoicesBtn.disabled) {
                    refreshVoiceList();
                }
            }
        });

        // 添加API密钥验证
        elements.apiKey.addEventListener('blur', async () => {
            const apiKey = getApiKey();
            if (apiKey && apiKey.length > 10) {
                const isValid = await testApiConnection();
                if (isValid) {
                    elements.apiKey.style.borderColor = '#10b981';
                    console.log('✅ API密钥验证成功');
                } else {
                    elements.apiKey.style.borderColor = '#ef4444';
                    console.log('❌ API密钥验证失败');
                }
            }
        });

        // 自动保存API密钥到localStorage
        elements.apiKey.addEventListener('input', () => {
            const apiKey = getApiKey();
            if (apiKey) {
                localStorage.setItem('siliconflow_api_key', apiKey);
            }
        });

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            console.log('🎙️ SiliconFlow 语音克隆调试工具已加载');
            console.log('📖 使用说明:');
            console.log('1. 配置 API 密钥');
            console.log('2. 上传参考音频文件');
            console.log('3. 获取音色列表');
            console.log('4. 使用音色生成语音');
            console.log('5. 管理和删除音色');
            console.log('');
            console.log('⌨️ 快捷键:');
            console.log('Ctrl/Cmd + Enter: 生成语音');
            console.log('Ctrl/Cmd + U: 选择文件');
            console.log('Ctrl/Cmd + R: 刷新音色列表');

            // 从localStorage恢复API密钥
            const savedApiKey = localStorage.getItem('siliconflow_api_key');
            if (savedApiKey) {
                elements.apiKey.value = savedApiKey;
                console.log('✅ 已恢复保存的API密钥');
            }

            // 如果有API密钥，自动获取音色列表
            if (getApiKey()) {
                setTimeout(() => {
                    refreshVoiceList();
                }, 1000);
            }

            // 初始化参数预览
            updateParamPreview();
        });

        // 试听音色功能
        async function testVoicePreview(voiceUri, previewText) {
            const apiKey = getApiKey();
            if (!apiKey) {
                alert('请先配置 API 密钥');
                return;
            }

            try {
                const requestBody = {
                    model: elements.voiceModel.value,
                    input: previewText || '这是一个音色预览测试。',
                    voice: voiceUri,
                    speed: 1.0, // 试听使用默认参数
                    gain: 0.0,
                    response_format: 'mp3',
                    sample_rate: 44100
                };

                const response = await fetch(`${getBaseUrl()}/audio/speech`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const contentType = response.headers.get('content-type');

                if (contentType?.includes('audio/')) {
                    const audioData = await response.arrayBuffer();
                    const audioBlob = new Blob([audioData], { type: 'audio/mpeg' });
                    const audioUrl = URL.createObjectURL(audioBlob);

                    // 创建临时音频播放器
                    const tempAudio = new Audio(audioUrl);
                    tempAudio.play();

                    // 播放完成后清理资源
                    tempAudio.addEventListener('ended', () => {
                        URL.revokeObjectURL(audioUrl);
                    });
                } else {
                    const data = await response.json();
                    if (data.url || data.audio_url) {
                        const tempAudio = new Audio(data.url || data.audio_url);
                        tempAudio.play();
                    }
                }

            } catch (error) {
                console.error('试听失败:', error);
                alert(`试听失败: ${error.message}`);
            }
        }

        // API使用统计
        let apiStats = {
            uploads: 0,
            generations: 0,
            deletions: 0,
            errors: 0
        };

        function updateApiStats(type, success = true) {
            if (success) {
                apiStats[type]++;
            } else {
                apiStats.errors++;
            }

            // 更新UI显示
            updateStatsDisplay();

            console.log('📊 API使用统计:', apiStats);
        }

        function updateStatsDisplay() {
            document.getElementById('uploadCount').textContent = apiStats.uploads;
            document.getElementById('generationCount').textContent = apiStats.generations;
            document.getElementById('deletionCount').textContent = apiStats.deletions;
            document.getElementById('errorCount').textContent = apiStats.errors;
        }

        function resetApiStats() {
            apiStats = {
                uploads: 0,
                generations: 0,
                deletions: 0,
                errors: 0
            };
            updateStatsDisplay();
            console.log('📊 API统计已重置');
        }

        // 增强错误处理
        function getDetailedErrorMessage(error, response) {
            if (response) {
                switch (response.status) {
                    case 400:
                        return '请求参数错误，请检查输入数据格式';
                    case 401:
                        return 'API密钥无效或已过期，请检查密钥配置';
                    case 403:
                        return '访问被拒绝，请检查API密钥权限';
                    case 404:
                        return 'API接口不存在，请检查接口地址';
                    case 429:
                        return '请求频率过高，请稍后重试';
                    case 500:
                        return 'SiliconFlow服务器内部错误';
                    case 502:
                    case 503:
                    case 504:
                        return 'SiliconFlow服务暂时不可用，请稍后重试';
                    default:
                        return `HTTP ${response.status}: ${error.message}`;
                }
            }

            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                return '网络连接失败，请检查网络连接或代理设置';
            }

            return error.message || '未知错误';
        }

        // 暴露函数到全局作用域
        window.deleteVoice = deleteVoice;
        window.testApiConnection = testApiConnection;
        window.testVoicePreview = testVoicePreview;
        window.resetApiStats = resetApiStats;
        window.resetToDefaults = resetToDefaults;
    </script>
</body>
</html>
